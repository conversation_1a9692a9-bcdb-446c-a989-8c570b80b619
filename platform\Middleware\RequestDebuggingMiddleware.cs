﻿namespace platform.Middleware
{
    public class RequestDebuggingMiddleware
    {
        private readonly RequestDelegate _next;

        public RequestDebuggingMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Log request details
            Console.WriteLine($"Incoming Request: {context.Request.Method} {context.Request.Path}");
            foreach (var header in context.Request.Headers)
            {
                Console.WriteLine($"Header: {header.Key} = {header.Value}");
            }
            // If you need to read the request body, be aware that it's a stream and can only be read once.
            // You might need to enable buffering or copy the stream.
            // For example:
            context.Request.EnableBuffering();
            var reader = new StreamReader(context.Request.Body);
            var body = await reader.ReadToEndAsync();
            Console.WriteLine($"Request Body: {body}");
            context.Request.Body.Position = 0; // Reset stream position if needed for subsequent middleware

            // You can also set breakpoints here in Visual Studio to inspect variables during debugging.

            await _next(context); // Pass the request to the next middleware in the pipeline
        }
    }

}
