using System.ComponentModel;

namespace shared.Models.Enums
{
    public enum ModelType
    {
        [Description("API")]
        APIKey = 1,
        
        [Description("AKS")]
        APIKeyStorageBaseModel = 2,
        
        [Description("ACC")]
        Account = 3,
        
        [Description("ACU")]
        AccountUser = 4,
        
        [Description("AGT")]
        Agent = 5,
        
        [Description("AGA")]
        AgentAlias = 6,
        
        [Description("ABT")]
        AgentBehaviorTreeDocument = 7,
        
        [Description("AKB")]
        AgentKnowledgeBase = 8,
        
        [Description("ATG")]
        AgentTag = 9,
        
        [Description("FLW")]
        Flow = 10,
        
        [Description("FED")]
        FlowExecutionDocument = 11,
        
        [Description("KNB")]
        KnowledgeBase = 12,
        
        [Description("KBF")]
        KnowledgeBaseFile = 13,
        
        [Description("SES")]
        Session = 14,
        
        [Description("SGP")]
        SignupProcess = 15,
        
        [Description("AWA")]
        AwsAgent = 16,
        
        [Description("AAA")]
        AwsAgentAlias = 17,
        
        [Description("AAD")]
        AwsAgentData = 18,
        
        [Description("AAT")]
        AwsAgentTag = 19,
        
        [Description("ATD")]
        AwsAgentTagData = 20,
        
        [Description("WKB")]
        AwsKnowledgeBase = 21,
        
        [Description("AKD")]
        AwsKnowledgeBaseData = 22,
        
        [Description("AWS")]
        AwsSession = 23,
        
        [Description("ASD")]
        AwsSessionData = 24
    }
}
