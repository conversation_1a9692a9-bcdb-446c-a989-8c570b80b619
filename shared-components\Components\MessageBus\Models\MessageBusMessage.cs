using shared.Converters;
using shared.Models.Enums;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace shared.Components.MessageBus.Models
{
    /// <summary>
    /// Internal message format used by message bus implementations for queue transport
    /// This contains queue-specific metadata that should not be exposed to the final route
    /// </summary>
    public class MessageBusMessage
    {
        /// <summary>
        /// Unique message identifier
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Source microservice identifier
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// Target microservice for routing
        /// </summary>
        [JsonConverter(typeof(JsonEnumStringConverter<MicroserviceType>))]
        public MicroserviceType TargetMicroservice { get; set; }

        /// <summary>
        /// Controller name for routing
        /// </summary>
        public string Controller { get; set; } = string.Empty;

        /// <summary>
        /// Route name for routing
        /// </summary>
        public string Route { get; set; } = string.Empty;

        /// <summary>
        /// HTTP method for the request
        /// </summary>
        [JsonConverter(typeof(JsonHttpMethodConverter))]
        public HttpMethod Method { get; set; } = HttpMethod.Post;

        /// <summary>
        /// The message payload as an object with automatic type conversion
        /// </summary>
        [JsonTypedConverter]
        public object Payload { get; set; } = new object();

        /// <summary>
        /// Security claims for authentication
        /// </summary>
        [JsonConverter(typeof(JsonListOfConverter<Claim, JsonClaimConverter>))]
        public List<Claim> Claims { get; set; } = new List<Claim>();

        /// <summary>
        /// Delay in seconds before processing
        /// </summary>
        public int DelayInSeconds { get; set; } = 0;

        /// <summary>
        /// Current retry count
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// Maximum retry attempts allowed
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Timestamp when message was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Timestamp when message was last processed
        /// </summary>
        public DateTime? LastProcessedAt { get; set; }

        /// <summary>
        /// Error message from last failed attempt
        /// </summary>
        public string? LastError { get; set; }



        /// <summary>
        /// Additional metadata for the message
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Default constructor
        /// </summary>
        public MessageBusMessage()
        {
        }





        /// <summary>
        /// Get the endpoint path for routing
        /// </summary>
        /// <returns>Combined controller/route path</returns>
        public string GetEndpoint()
        {
            return $"{Controller}/{Route}";
        }

        /// <summary>
        /// Check if message can be retried
        /// </summary>
        /// <returns>True if retry is possible</returns>
        public bool CanRetry()
        {
            return RetryCount < MaxRetries;
        }

        /// <summary>
        /// Increment retry count and update metadata
        /// </summary>
        public void IncrementRetry(string? errorMessage = null)
        {
            RetryCount++;
            LastProcessedAt = DateTime.UtcNow;
            LastError = errorMessage;
        }

        /// <summary>
        /// Mark message as completed
        /// </summary>
        public void MarkCompleted()
        {
            LastProcessedAt = DateTime.UtcNow;
            LastError = null;
        }

        /// <summary>
        /// Mark message as failed
        /// </summary>
        /// <param name="errorMessage">Error description</param>
        public void MarkFailed(string? errorMessage = null)
        {
            LastProcessedAt = DateTime.UtcNow;
            LastError = errorMessage;
        }

        /// <summary>
        /// Get the payload as a specific type
        /// </summary>
        /// <typeparam name="T">The type to convert the payload to</typeparam>
        /// <returns>The payload converted to type T</returns>
        /// <exception cref="InvalidCastException">Thrown when the payload cannot be converted to type T</exception>
        public T GetPayload<T>()
        {
            if (Payload is T directCast)
            {
                return directCast;
            }

            // Try to convert using JsonSerializer if it's a JsonElement
            if (Payload is JsonElement jsonElement)
            {
                return jsonElement.Deserialize<T>() ?? throw new InvalidCastException($"Cannot convert payload to type {typeof(T).Name}");
            }

            // Try direct conversion
            try
            {
                return (T)Convert.ChangeType(Payload, typeof(T));
            }
            catch (Exception ex)
            {
                throw new InvalidCastException($"Cannot convert payload of type {Payload?.GetType().Name ?? "null"} to type {typeof(T).Name}", ex);
            }
        }
    }
}
