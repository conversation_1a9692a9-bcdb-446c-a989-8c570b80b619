using shared.Models.Enums;

namespace shared.Services
{
    /// <summary>
    /// Service for generating unique IDs for models across microservices.
    /// </summary>
    public interface IUniqueIDService
    {
        /// <summary>
        /// Generates a unique ID by concatenating microservice type, instance ID, model type prefix, and a base64-encoded UUID.
        /// </summary>
        /// <param name="modelType">The type of model for which to generate the ID</param>
        /// <returns>A unique ID string</returns>
        Task<string> GenerateUniqueIdAsync(ModelType modelType);
    }
}
