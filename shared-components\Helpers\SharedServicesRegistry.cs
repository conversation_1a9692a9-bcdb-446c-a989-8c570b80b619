﻿using shared.Services;
using shared.Services.Implementation;

namespace shared.Helpers
{
    public static class SharedServicesRegistry
    {
        /// <summary>
        /// Adds the UniqueIDService to the service collection.
        /// Registers IUniqueIDService interface with UniqueIDService implementation.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddUniqueIDService(this IServiceCollection services)
        {
            services.AddScoped<IUniqueIDService, UniqueIDService>();
            return services;
        }
    }
}
